<?php

namespace Tests\Unit;

use App\Models\DTOAccountingData;
use App\Models\DTOBankData;
use App\Models\DTOTenantMetadata;
use PHPUnit\Framework\TestCase;

class DTOTenantMetadataTest extends TestCase
{
    private array $sampleData;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->sampleData = [
            "accounting" => [
                "regon" => "*********",
                "bdo" => "FDFD54545"
            ],
            "bank_accounts" => [
                [
                    "account_name" => "Mbank PLN",
                    "bank_name" => "Mbank",
                    "bank_account" => "25 2122 4545 3232 1414 2523 5865",
                    "bank_swift" => "sfdsfdsdf",
                    "bank_iban" => null,
                    "bank_currency" => "PLN"
                ],
                [
                    "account_name" => "Alior EUR",
                    "bank_name" => "Alior bank",
                    "bank_account" => "27 2122 4545 3232 1414 2523 5867",
                    "bank_swift" => "ALEBTR2323",
                    "bank_iban" => null,
                    "bank_currency" => "EUR"
                ]
            ]
        ];
    }

    public function test_can_create_from_array()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        
        $this->assertInstanceOf(DTOTenantMetadata::class, $dto);
        $this->assertEquals('*********', $dto->getAccounting()->regon);
        $this->assertEquals('FDFD54545', $dto->getAccounting()->bdo);
        $this->assertCount(2, $dto->getBankAccounts());
    }

    public function test_can_create_from_json()
    {
        $json = json_encode($this->sampleData);
        $dto = DTOTenantMetadata::fromJson($json);
        
        $this->assertInstanceOf(DTOTenantMetadata::class, $dto);
        $this->assertEquals('*********', $dto->getAccounting()->regon);
        $this->assertEquals('FDFD54545', $dto->getAccounting()->bdo);
    }

    public function test_can_convert_to_array()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        $array = $dto->toArray();
        
        $this->assertEquals($this->sampleData, $array);
    }

    public function test_can_convert_to_json()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        $json = $dto->toJson();
        
        $this->assertJson($json);
        $this->assertEquals($this->sampleData, json_decode($json, true));
    }

    public function test_can_find_bank_account_by_name()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        $bankAccount = $dto->getBankAccountByName('Mbank PLN');
        
        $this->assertInstanceOf(DTOBankData::class, $bankAccount);
        $this->assertEquals('Mbank PLN', $bankAccount->account_name);
        $this->assertEquals('PLN', $bankAccount->bank_currency);
    }

    public function test_can_find_bank_account_by_number()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        $bankAccount = $dto->getBankAccountByNumber('25 2122 4545 3232 1414 2523 5865');
        
        $this->assertInstanceOf(DTOBankData::class, $bankAccount);
        $this->assertEquals('Mbank PLN', $bankAccount->account_name);
    }

    public function test_can_filter_bank_accounts_by_currency()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        $plnAccounts = $dto->getBankAccountsByCurrency('PLN');
        
        $this->assertCount(1, $plnAccounts);
        $this->assertEquals('Mbank PLN', $plnAccounts->first()->account_name);
    }

    public function test_handles_nullable_bank_iban()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        $bankAccount = $dto->getBankAccountByName('Mbank PLN');
        
        $this->assertNull($bankAccount->bank_iban);
    }

    public function test_validation_passes_for_valid_data()
    {
        $dto = DTOTenantMetadata::make($this->sampleData);
        
        $this->assertTrue($dto->isValid());
        $this->assertEmpty($dto->validate());
    }

    public function test_validation_fails_for_missing_regon()
    {
        $invalidData = $this->sampleData;
        $invalidData['accounting']['regon'] = '';
        
        $dto = DTOTenantMetadata::make($invalidData);
        
        $this->assertFalse($dto->isValid());
        $this->assertContains('Accounting REGON is required', $dto->validate());
    }

    public function test_validation_fails_for_empty_bank_accounts()
    {
        $invalidData = $this->sampleData;
        $invalidData['bank_accounts'] = [];
        
        $dto = DTOTenantMetadata::make($invalidData);
        
        $this->assertFalse($dto->isValid());
        $this->assertContains('At least one bank account is required', $dto->validate());
    }
}
