<?php

if (! function_exists('tenant')) {
    function tenant(bool $refresh = false): ?\App\Models\Tenant
    {
        if (null === auth()->user()) {
            return null;
        }

        if ($refresh || !session()->has(auth()->user()->id . '_tenant')) {
            $tenant = auth()->user()->getTenant();
            if (null === $tenant) {
                return null;
            }

            session()->put(auth()->user()->id . '_tenant', $tenant);
        }

        return session()->get(auth()->user()->id . '_tenant', null);
    }
}

if (! function_exists('tenantFlush')) {
    function tenantFlush(): void
    {
        if (null === auth()->getUser()) {
            return;
        }

        session()->forget(auth()->user()->id . '_tenant');
    }
}

if (! function_exists('appVersion')) {
    function appVersion(bool $refresh = false): array
    {
        [$major, $minor, $patch] = explode('.', config('app.version'));
        return ['major' => $major, 'minor' => $minor, 'patch' => $patch];
    }
}


function imageFileToBase64(string $path): string
{

    $imageData = \Illuminate\Support\Facades\File::get($path);
    if (empty($imageData)) {
        return '';
    }

    $mimeType = \Illuminate\Support\Facades\File::mimeType($path);

    if (empty($mimeType)) {
        trigger_error("Nie można rozpoznać typu MIME dla pliku", E_USER_WARNING);
        return '';
    }

    $base64 = base64_encode($imageData);
    if ($base64 === false) {
        trigger_error("Nie można zakodować danych obrazu w Base64.", E_USER_WARNING);
        return '';
    }

    return 'data:' . $mimeType . ';base64,' . $base64;
}

/**
 * Get tenant logo as base64-encoded image data
 *
 * @param \App\Models\Tenant $tenant
 * @return string Base64-encoded image data for use in HTML img src tags, empty string if no logo
 */
function tenantLogo(\App\Models\Tenant $tenant): string
{
    try {
        // Get tenant metadata
        $metaData = $tenant->meta?->meta ?? [];
        if (empty($metaData)) {
            return '';
        }

        // Create DTO from metadata
        $metadataDto = \App\Models\DTOTenantMetadata::make($metaData);

        // Get logo image data
        $logoImage = $metadataDto->getImageByType('logo');
        if (!$logoImage) {
            return '';
        }

        // Build the file path
        $logoPath = storage_path("app/tenants/{$tenant->hash}/images/{$logoImage->filename}");

        // Check if file exists
        if (!\Illuminate\Support\Facades\File::exists($logoPath)) {
            return '';
        }

        // Return base64-encoded image
        return imageFileToBase64($logoPath);
    } catch (\Exception $e) {
        // Log error and return empty string
        \Illuminate\Support\Facades\Log::warning('Failed to load tenant logo: ' . $e->getMessage());
        return '';
    }
}
