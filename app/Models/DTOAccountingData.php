<?php

namespace App\Models;

class DTOAccountingData implements \JsonSerializable
{
    public function __construct(
        public readonly string $regon,
        public readonly string $bdo,
    ) {
    }

    public static function make(array $data): self
    {
        return new self(
            regon: $data['regon'] ?? '',
            bdo: $data['bdo'] ?? '',
        );
    }

    public function toArray(): array
    {
        return [
            'regon' => $this->regon,
            'bdo' => $this->bdo,
        ];
    }

    public function jsonSerialize(): mixed
    {
        return $this->toArray();
    }
}
