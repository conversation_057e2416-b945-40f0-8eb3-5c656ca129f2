<?php

namespace App\Models;

class DTOImageData implements \JsonSerializable
{
    public function __construct(
        public readonly string $type,
        public readonly string $filename,
        public readonly string $path_filename,
        public readonly string $upload_timestamp,
    ) {
    }

    public static function make(array $data): self
    {
        return new self(
            type: $data['type'] ?? '',
            filename: $data['filename'] ?? '',
            path_filename: $data['path_filename'] ?? '',
            upload_timestamp: $data['upload_timestamp'] ?? '',
        );
    }

    public function toArray(): array
    {
        return [
            'type' => $this->type,
            'filename' => $this->filename,
            'path_filename' => $this->path_filename,
            'upload_timestamp' => $this->upload_timestamp,
        ];
    }

    public function jsonSerialize(): mixed
    {
        return $this->toArray();
    }
}
