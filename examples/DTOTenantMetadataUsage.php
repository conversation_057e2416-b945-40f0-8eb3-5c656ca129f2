<?php

/**
 * DTOTenantMetadata Usage Examples
 * 
 * This file demonstrates how to use the DTOTenantMetadata class
 * for handling tenant metadata in the application.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Models\DTOTenantMetadata;
use App\Models\DTOAccountingData;
use App\Models\DTOBankData;

// Example 1: Creating from array data (typical use case)
echo "=== Example 1: Creating from Array ===\n";

$metadataArray = [
    "accounting" => [
        "regon" => "*********",
        "bdo" => "FDFD54545"
    ],
    "bank_accounts" => [
        [
            "account_name" => "Mbank PLN",
            "bank_name" => "Mbank",
            "bank_account" => "25 2122 4545 3232 1414 2523 5865",
            "bank_swift" => "sfdsfdsdf",
            "bank_iban" => null,
            "bank_currency" => "PLN"
        ],
        [
            "account_name" => "Alior EUR",
            "bank_name" => "Alior bank",
            "bank_account" => "27 2122 4545 3232 1414 2523 5867",
            "bank_swift" => "ALEBTR2323",
            "bank_iban" => null,
            "bank_currency" => "EUR"
        ]
    ]
];

$tenantMetadata = DTOTenantMetadata::make($metadataArray);
echo "Created DTO with REGON: " . $tenantMetadata->getAccounting()->regon . "\n";
echo "Number of bank accounts: " . $tenantMetadata->getBankAccounts()->count() . "\n\n";

// Example 2: Creating from JSON (useful for API endpoints)
echo "=== Example 2: Creating from JSON ===\n";

$jsonString = json_encode($metadataArray);
$tenantMetadataFromJson = DTOTenantMetadata::fromJson($jsonString);
echo "Created from JSON - BDO: " . $tenantMetadataFromJson->getAccounting()->bdo . "\n\n";

// Example 3: Converting back to array/JSON for database storage
echo "=== Example 3: Converting for Database Storage ===\n";

$arrayForDb = $tenantMetadata->toArray();
$jsonForDb = $tenantMetadata->toJson();

echo "Array format ready for database:\n";
print_r($arrayForDb);
echo "\nJSON format ready for database:\n";
echo $jsonForDb . "\n\n";

// Example 4: Finding specific bank accounts
echo "=== Example 4: Finding Bank Accounts ===\n";

$mbankAccount = $tenantMetadata->getBankAccountByName('Mbank PLN');
if ($mbankAccount) {
    echo "Found Mbank account: " . $mbankAccount->bank_account . "\n";
}

$accountByNumber = $tenantMetadata->getBankAccountByNumber('27 2122 4545 3232 1414 2523 5867');
if ($accountByNumber) {
    echo "Found account by number: " . $accountByNumber->account_name . "\n";
}

$eurAccounts = $tenantMetadata->getBankAccountsByCurrency('EUR');
echo "EUR accounts found: " . $eurAccounts->count() . "\n\n";

// Example 5: Adding new bank account (immutable pattern)
echo "=== Example 5: Adding Bank Account ===\n";

$newBankAccount = new DTOBankData(
    account_name: 'PKO USD',
    bank_name: 'PKO Bank Polski',
    bank_account: '12 3456 7890 1234 5678 9012 3456',
    bank_swift: 'PKOPPLPW',
    bank_iban: '****************************',
    bank_currency: 'USD'
);

$updatedMetadata = $tenantMetadata->addBankAccount($newBankAccount);
echo "Original accounts: " . $tenantMetadata->getBankAccounts()->count() . "\n";
echo "Updated accounts: " . $updatedMetadata->getBankAccounts()->count() . "\n\n";

// Example 6: Validation
echo "=== Example 6: Validation ===\n";

if ($tenantMetadata->isValid()) {
    echo "✓ Tenant metadata is valid\n";
} else {
    echo "✗ Validation errors:\n";
    foreach ($tenantMetadata->validate() as $error) {
        echo "  - $error\n";
    }
}

// Example of invalid data
$invalidData = [
    "accounting" => [
        "regon" => "", // Empty REGON
        "bdo" => "FDFD54545"
    ],
    "bank_accounts" => [] // No bank accounts
];

$invalidMetadata = DTOTenantMetadata::make($invalidData);
echo "\nValidating invalid data:\n";
if (!$invalidMetadata->isValid()) {
    echo "✗ Validation errors found:\n";
    foreach ($invalidMetadata->validate() as $error) {
        echo "  - $error\n";
    }
}

echo "\n=== Example 7: Usage in Laravel Model ===\n";
echo "// In your Tenant model or service:\n";
echo "\$tenant = Tenant::find(1);\n";
echo "\$metadataDto = DTOTenantMetadata::make(\$tenant->meta->meta ?? []);\n";
echo "\n// To save back to database:\n";
echo "\$tenant->meta()->updateOrCreate(\n";
echo "    ['tenant_id' => \$tenant->id],\n";
echo "    ['meta' => \$metadataDto->toArray()]\n";
echo ");\n";
