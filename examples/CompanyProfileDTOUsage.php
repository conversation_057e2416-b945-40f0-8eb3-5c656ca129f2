<?php

/**
 * CompanyProfile DTO Integration Usage Examples
 * 
 * This file demonstrates how the updated CompanyProfile class
 * now uses DTOs for handling tenant metadata.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Filament\App\Pages\CompanyProfile;
use App\Models\Company;
use App\Models\DTOTenantMetadata;
use App\Models\DTOAccountingData;
use App\Models\DTOBankData;

echo "=== CompanyProfile DTO Integration Examples ===\n\n";

// Example 1: How the save() method now works with DTO validation
echo "=== Example 1: Save Method with DTO Validation ===\n";
echo "The save() method now:\n";
echo "1. Creates a DTOTenantMetadata from form data\n";
echo "2. Validates the DTO before saving\n";
echo "3. Shows validation errors if any\n";
echo "4. Saves validated data to database\n\n";

echo "Code flow in save() method:\n";
echo "\$metadataDto = DTOTenantMetadata::make(\$meta);\n";
echo "if (!\$metadataDto->isValid()) {\n";
echo "    // Show validation errors\n";
echo "    return false;\n";
echo "}\n";
echo "// Save validated data\n";
echo "\$this->getRecord()->meta()->updateOrCreate(..., ['meta' => \$metadataDto->toArray()]);\n\n";

// Example 2: Form data preparation with DTO
echo "=== Example 2: Form Data Preparation ===\n";
echo "The mutateFormDataBeforeFill() method now:\n";
echo "1. Loads metadata from database\n";
echo "2. Creates DTO from metadata\n";
echo "3. Converts DTO back to array for form compatibility\n";
echo "4. Provides default structure if no metadata exists\n\n";

$sampleFormData = [
    'meta' => [
        'accounting' => [
            'regon' => '*********',
            'bdo' => 'FDFD54545'
        ],
        'bank_accounts' => [
            [
                'account_name' => 'Mbank PLN',
                'bank_name' => 'Mbank',
                'bank_account' => '25 2122 4545 3232 1414 2523 5865',
                'bank_swift' => 'sfdsfdsdf',
                'bank_iban' => null,
                'bank_currency' => 'PLN'
            ]
        ]
    ]
];

echo "Sample form data structure:\n";
print_r($sampleFormData);

// Example 3: New helper methods
echo "\n=== Example 3: New Helper Methods ===\n";
echo "The CompanyProfile class now provides these helper methods:\n\n";

echo "1. getTenantMetadataDto(): ?DTOTenantMetadata\n";
echo "   - Returns the current tenant metadata as a DTO\n";
echo "   - Returns null if no metadata exists\n\n";

echo "2. getBankAccounts(): Collection\n";
echo "   - Returns collection of DTOBankData objects\n";
echo "   - Returns empty collection if no metadata\n\n";

echo "3. getAccountingData(): ?DTOAccountingData\n";
echo "   - Returns accounting data as DTO\n";
echo "   - Returns null if no metadata exists\n\n";

// Example 4: Usage in controllers or other classes
echo "=== Example 4: Usage in Controllers ===\n";
echo "// In a controller or service class:\n";
echo "\$companyProfile = new CompanyProfile();\n";
echo "\$companyProfile->record(\$company);\n\n";

echo "// Get all bank accounts\n";
echo "\$bankAccounts = \$companyProfile->getBankAccounts();\n";
echo "foreach (\$bankAccounts as \$account) {\n";
echo "    echo \$account->account_name . ': ' . \$account->bank_currency;\n";
echo "}\n\n";

echo "// Get accounting data\n";
echo "\$accounting = \$companyProfile->getAccountingData();\n";
echo "if (\$accounting) {\n";
echo "    echo 'REGON: ' . \$accounting->regon;\n";
echo "    echo 'BDO: ' . \$accounting->bdo;\n";
echo "}\n\n";

echo "// Get full metadata DTO\n";
echo "\$metadataDto = \$companyProfile->getTenantMetadataDto();\n";
echo "if (\$metadataDto) {\n";
echo "    \$plnAccounts = \$metadataDto->getBankAccountsByCurrency('PLN');\n";
echo "    \$mbankAccount = \$metadataDto->getBankAccountByName('Mbank PLN');\n";
echo "}\n\n";

// Example 5: Error handling
echo "=== Example 5: Error Handling ===\n";
echo "The updated CompanyProfile handles errors gracefully:\n\n";

echo "1. DTO Creation Errors:\n";
echo "   - If DTO creation fails, falls back to raw data\n";
echo "   - Logs errors for debugging\n\n";

echo "2. Validation Errors:\n";
echo "   - Shows user-friendly validation messages\n";
echo "   - Prevents saving invalid data\n\n";

echo "3. Database Errors:\n";
echo "   - Catches exceptions during save\n";
echo "   - Shows error notifications to user\n\n";

// Example 6: Benefits of the DTO integration
echo "=== Example 6: Benefits of DTO Integration ===\n";
echo "1. Type Safety:\n";
echo "   - All metadata is now properly typed\n";
echo "   - IDE autocompletion and error detection\n\n";

echo "2. Validation:\n";
echo "   - Automatic validation before saving\n";
echo "   - Consistent validation rules\n\n";

echo "3. Data Integrity:\n";
echo "   - Ensures data structure consistency\n";
echo "   - Handles nullable fields properly\n\n";

echo "4. Maintainability:\n";
echo "   - Centralized metadata handling\n";
echo "   - Easy to extend and modify\n\n";

echo "5. Reusability:\n";
echo "   - DTOs can be used across the application\n";
echo "   - Consistent data handling patterns\n\n";

// Example 7: Form validation integration
echo "=== Example 7: Form Validation Integration ===\n";
echo "The form now benefits from DTO validation:\n\n";

$invalidData = [
    'accounting' => [
        'regon' => '', // Invalid: empty
        'bdo' => 'FDFD54545'
    ],
    'bank_accounts' => [] // Invalid: no accounts
];

echo "Invalid data example:\n";
print_r($invalidData);

echo "\nValidation would catch:\n";
echo "- Empty REGON field\n";
echo "- Missing bank accounts\n";
echo "- Invalid bank account data\n\n";

echo "User would see: 'Błędy walidacji: Sprawdź poprawność danych: Accounting REGON is required, At least one bank account is required'\n\n";

echo "=== Summary ===\n";
echo "The CompanyProfile class now:\n";
echo "✓ Uses DTOs for type-safe metadata handling\n";
echo "✓ Validates data before saving\n";
echo "✓ Provides helpful error messages\n";
echo "✓ Maintains backward compatibility\n";
echo "✓ Offers convenient helper methods\n";
echo "✓ Handles edge cases gracefully\n";
