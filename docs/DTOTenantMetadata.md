# DTOTenantMetadata Documentation

## Overview

The `DTOTenantMetadata` class is a comprehensive Data Transfer Object (DTO) designed to handle tenant metadata that is stored in the database as a JSON object. It provides a structured, type-safe way to work with tenant accounting information and bank account data.

## Structure

The DTO handles the following JSON structure:

```json
{
  "accounting": {
    "regon": "*********",
    "bdo": "FDFD54545"
  },
  "bank_accounts": [
    {
      "account_name": "Mbank PLN",
      "bank_name": "Mbank",
      "bank_account": "25 2122 4545 3232 1414 2523 5865",
      "bank_swift": "sfdsfdsdf",
      "bank_iban": null,
      "bank_currency": "PLN"
    }
  ]
}
```

## Classes

### DTOTenantMetadata

Main DTO class that contains:
- `DTOAccountingData $accounting` - Accounting information
- `Collection<DTOBankData> $bank_accounts` - Collection of bank accounts

### DTOAccountingData

Handles accounting information:
- `string $regon` - REGON number
- `string $bdo` - BDO identifier

### DTOBankData (Updated)

Handles bank account information:
- `string $account_name` - Account name
- `string $bank_name` - Bank name
- `string $bank_account` - Account number
- `string $bank_swift` - SWIFT code
- `?string $bank_iban` - IBAN (nullable)
- `string $bank_currency` - Currency code

## Key Features

### 1. Type Safety
All properties are properly typed with PHP type declarations.

### 2. Immutability
DTOs follow immutable patterns - operations return new instances rather than modifying existing ones.

### 3. Serialization Support
- `toArray()` - Convert to array format
- `toJson()` - Convert to JSON string
- `jsonSerialize()` - JsonSerializable interface implementation

### 4. Deserialization Support
- `make(array $data)` - Create from array
- `fromArray(array $data)` - Alias for make()
- `fromJson(string $json)` - Create from JSON string

### 5. Validation
- `validate()` - Returns array of validation errors
- `isValid()` - Returns boolean validation status

### 6. Query Methods
- `getBankAccountByName(string $name)` - Find account by name
- `getBankAccountByNumber(string $number)` - Find account by number
- `getBankAccountsByCurrency(string $currency)` - Filter by currency

### 7. Manipulation Methods
- `addBankAccount(DTOBankData $bankData)` - Add new bank account
- `removeBankAccountByName(string $name)` - Remove account by name
- `updateAccounting(DTOAccountingData $accounting)` - Update accounting data

## Usage Examples

### Creating from Array
```php
$data = [
    "accounting" => ["regon" => "*********", "bdo" => "ABC123"],
    "bank_accounts" => [/* bank account data */]
];

$dto = DTOTenantMetadata::make($data);
```

### Creating from JSON
```php
$json = '{"accounting":{"regon":"*********","bdo":"ABC123"},"bank_accounts":[]}';
$dto = DTOTenantMetadata::fromJson($json);
```

### Converting for Database Storage
```php
// Store as array in database
$tenant->meta()->updateOrCreate(
    ['tenant_id' => $tenant->id],
    ['meta' => $dto->toArray()]
);

// Or as JSON
$jsonString = $dto->toJson();
```

### Finding Bank Accounts
```php
$account = $dto->getBankAccountByName('Mbank PLN');
$eurAccounts = $dto->getBankAccountsByCurrency('EUR');
```

### Validation
```php
if (!$dto->isValid()) {
    $errors = $dto->validate();
    foreach ($errors as $error) {
        echo $error . "\n";
    }
}
```

## Integration with Existing Code

### Tenant Model Integration
The existing `Tenant` model already has a `getBankAccounts()` method that can be enhanced:

```php
// In Tenant model
public function getMetadataDto(): ?DTOTenantMetadata
{
    if (!$this->meta?->meta) {
        return null;
    }
    
    return DTOTenantMetadata::make($this->meta->meta);
}

public function updateMetadata(DTOTenantMetadata $metadata): void
{
    $this->meta()->updateOrCreate(
        ['tenant_id' => $this->id],
        ['meta' => $metadata->toArray()]
    );
}
```

### TradeDocMeta Integration
The existing `TradeDocMeta` model can continue using `DTOBankData` as before, since we maintained backward compatibility.

## Validation Rules

### Accounting Data
- `regon` - Required, non-empty string
- `bdo` - Required, non-empty string

### Bank Accounts
- At least one bank account is required
- Each bank account must have:
  - `account_name` - Required, non-empty string
  - `bank_name` - Required, non-empty string
  - `bank_account` - Required, non-empty string
  - `bank_currency` - Required, non-empty string
  - `bank_swift` - Optional string
  - `bank_iban` - Optional, nullable string

## Error Handling

### JSON Parsing Errors
```php
try {
    $dto = DTOTenantMetadata::fromJson($invalidJson);
} catch (\InvalidArgumentException $e) {
    // Handle JSON parsing error
    echo "Invalid JSON: " . $e->getMessage();
}
```

### Validation Errors
```php
$dto = DTOTenantMetadata::make($data);
if (!$dto->isValid()) {
    $errors = $dto->validate();
    // Handle validation errors
}
```

## Testing

Comprehensive unit tests are provided in `tests/Unit/DTOTenantMetadataTest.php` covering:
- Creation from array and JSON
- Conversion to array and JSON
- Bank account querying
- Validation scenarios
- Nullable field handling

Run tests with:
```bash
./server artisan test tests/Unit/DTOTenantMetadataTest.php
```

## Best Practices

1. **Always validate** DTOs before storing to database
2. **Use type hints** when working with DTOs in your code
3. **Handle nullable fields** appropriately (especially `bank_iban`)
4. **Use immutable patterns** - create new instances for modifications
5. **Leverage query methods** for finding specific bank accounts
6. **Test thoroughly** when integrating with existing code
