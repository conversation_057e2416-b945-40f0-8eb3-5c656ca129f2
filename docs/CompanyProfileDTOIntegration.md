# CompanyProfile DTO Integration

## Overview

The `App\Filament\App\Pages\CompanyProfile` class has been updated to use the new DTO (Data Transfer Object) system for handling tenant metadata. This integration provides type safety, validation, and better data integrity when working with tenant metadata stored in the `TenantMeta` model.

## Changes Made

### 1. Added DTO Imports

```php
use App\Models\DTOTenantMetadata;
use App\Models\DTOAccountingData;
use App\Models\DTOBankData;
```

### 2. Updated `mutateFormDataBeforeFill()` Method

**Before:**
```php
protected function mutateFormDataBeforeFill(array $data): array
{
    unset($data['id'], $data['config'], $data['is_active'], $data['created_at'], $data['updated_at']);
    $data['meta'] = $data['meta']['meta'] ?? [];
    return $data;
}
```

**After:**
```php
protected function mutateFormDataBeforeFill(array $data): array
{
    unset($data['id'], $data['config'], $data['is_active'], $data['created_at'], $data['updated_at']);
    
    // Convert metadata to DTO and then back to array for form compatibility
    $metaData = $data['meta']['meta'] ?? [];
    if (!empty($metaData)) {
        try {
            $metadataDto = DTOTenantMetadata::make($metaData);
            $data['meta'] = $metadataDto->toArray();
        } catch (\Exception $e) {
            // Fallback to raw data if DTO creation fails
            $data['meta'] = $metaData;
        }
    } else {
        $data['meta'] = [
            'accounting' => ['regon' => '', 'bdo' => ''],
            'bank_accounts' => []
        ];
    }
    
    return $data;
}
```

**Benefits:**
- Validates metadata structure on load
- Provides default structure for new companies
- Graceful fallback for invalid data

### 3. Enhanced `save()` Method

**Before:**
```php
public function save()
{
    $data = $this->getForm('form')->getState();
    $meta = $data['meta'] ?? [];
    unset($data['meta']);
    $this->getRecord()->update($data);
    if ($meta) {
        $this->getRecord()
            ->meta()
            ->updateOrCreate(
                ['tenant_id' => $this->getRecord()->id],
                ['meta' => $meta]
            );
        \tenant(true);
    }

    Notification::make()
        ->title('Zapisano pomyślnie')
        ->success()
        ->send();
    return true;
}
```

**After:**
```php
public function save()
{
    $data = $this->getForm('form')->getState();
    $meta = $data['meta'] ?? [];
    unset($data['meta']);
    
    // Update basic tenant data
    $this->getRecord()->update($data);
    
    // Handle metadata using DTO
    if ($meta) {
        try {
            // Create DTO from form data
            $metadataDto = DTOTenantMetadata::make($meta);
            
            // Validate the DTO
            if (!$metadataDto->isValid()) {
                $errors = $metadataDto->validate();
                Notification::make()
                    ->title('Błędy walidacji')
                    ->body('Sprawdź poprawność danych: ' . implode(', ', $errors))
                    ->danger()
                    ->send();
                return false;
            }
            
            // Save validated metadata to database
            $this->getRecord()
                ->meta()
                ->updateOrCreate(
                    ['tenant_id' => $this->getRecord()->id],
                    ['meta' => $metadataDto->toArray()]
                );
                
            \tenant(true);
            
        } catch (\Exception $e) {
            Notification::make()
                ->title('Błąd podczas zapisywania')
                ->body('Wystąpił błąd podczas przetwarzania danych: ' . $e->getMessage())
                ->danger()
                ->send();
            return false;
        }
    }

    Notification::make()
        ->title('Zapisano pomyślnie')
        ->success()
        ->send();
    return true;
}
```

**Benefits:**
- Validates data before saving
- Shows user-friendly error messages
- Prevents invalid data from being stored
- Better error handling

### 4. Added Helper Methods

```php
/**
 * Get the current tenant metadata as DTO
 */
public function getTenantMetadataDto(): ?DTOTenantMetadata
{
    $metaData = $this->getRecord()->meta?->meta ?? [];
    
    if (empty($metaData)) {
        return null;
    }
    
    try {
        return DTOTenantMetadata::make($metaData);
    } catch (\Exception $e) {
        return null;
    }
}

/**
 * Get bank accounts from current tenant metadata
 */
public function getBankAccounts(): \Illuminate\Support\Collection
{
    $dto = $this->getTenantMetadataDto();
    return $dto ? $dto->getBankAccounts() : collect();
}

/**
 * Get accounting data from current tenant metadata
 */
public function getAccountingData(): ?DTOAccountingData
{
    $dto = $this->getTenantMetadataDto();
    return $dto ? $dto->getAccounting() : null;
}
```

**Benefits:**
- Easy access to typed metadata
- Convenient methods for common operations
- Null-safe operations

## Usage Examples

### Getting Bank Accounts
```php
$companyProfile = new CompanyProfile();
$companyProfile->record($company);

$bankAccounts = $companyProfile->getBankAccounts();
foreach ($bankAccounts as $account) {
    echo $account->account_name . ': ' . $account->bank_currency;
}
```

### Getting Accounting Data
```php
$accounting = $companyProfile->getAccountingData();
if ($accounting) {
    echo 'REGON: ' . $accounting->regon;
    echo 'BDO: ' . $accounting->bdo;
}
```

### Working with Full Metadata DTO
```php
$metadataDto = $companyProfile->getTenantMetadataDto();
if ($metadataDto) {
    $plnAccounts = $metadataDto->getBankAccountsByCurrency('PLN');
    $mbankAccount = $metadataDto->getBankAccountByName('Mbank PLN');
}
```

## Validation

The integration now provides automatic validation:

### Required Fields
- Accounting REGON
- Accounting BDO
- At least one bank account
- Bank account name, bank name, account number, and currency for each account

### Error Messages
- Polish language error messages
- User-friendly notifications
- Detailed validation feedback

### Example Validation Errors
```
"Błędy walidacji: Sprawdź poprawność danych: Accounting REGON is required, At least one bank account is required"
```

## Backward Compatibility

The integration maintains full backward compatibility:
- Existing form structure unchanged
- Existing data continues to work
- Graceful handling of legacy data
- Fallback mechanisms for edge cases

## Benefits

### 1. Type Safety
- All metadata operations are now type-safe
- IDE autocompletion and error detection
- Reduced runtime errors

### 2. Data Validation
- Automatic validation before saving
- Consistent validation rules
- User-friendly error messages

### 3. Data Integrity
- Ensures consistent data structure
- Proper handling of nullable fields
- Prevents malformed data

### 4. Maintainability
- Centralized metadata handling
- Easy to extend and modify
- Clear separation of concerns

### 5. Developer Experience
- Convenient helper methods
- Better debugging capabilities
- Consistent API across the application

## Testing

The integration has been tested with:
- Valid metadata scenarios
- Invalid data handling
- Edge cases (empty data, malformed data)
- Error conditions

## Migration Notes

No database migration is required. The changes are:
- Backward compatible
- Non-breaking
- Additive only

Existing tenant metadata will continue to work without any changes.
